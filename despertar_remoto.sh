#!/bin/bash

# Script para despertar Plasma desde internet via SSH
# Requiere un servidor/dispositivo siempre encendido en red local

echo "🌐 === WAKE-ON-LAN REMOTO ==="
echo ""

# Configuración
SERVIDOR_INTERMEDIO="tu_servidor.ddns.net"  # O IP pública
PUERTO_SSH="22"
USUARIO_SSH="usuario"
PLASMA_MAC="fc:34:97:e1:9a:9b"
PLASMA_IP="*************"

echo "🔧 Configuración:"
echo "Servidor intermedio: $SERVIDOR_INTERMEDIO"
echo "Usuario SSH: $USUARIO_SSH"
echo "Objetivo: $PLASMA_IP ($PLASMA_MAC)"
echo ""

# Verificar conectividad al servidor intermedio
echo "🔍 Verificando conexión al servidor intermedio..."
if ! ssh -o ConnectTimeout=10 -p $PUERTO_SSH $USUARIO_SSH@$SERVIDOR_INTERMEDIO "echo 'Conexión OK'" 2>/dev/null; then
    echo "❌ No se puede conectar al servidor intermedio"
    echo ""
    echo "🛠️ Verifica:"
    echo "1. Que el servidor esté encendido"
    echo "2. Configuración SSH correcta"
    echo "3. Port forwarding del puerto $PUERTO_SSH"
    echo "4. Credenciales SSH"
    exit 1
fi

echo "✅ Conexión al servidor intermedio OK"
echo ""

# Ejecutar WOL desde el servidor intermedio
echo "📡 Enviando Wake-On-LAN desde servidor intermedio..."

ssh -p $PUERTO_SSH $USUARIO_SSH@$SERVIDOR_INTERMEDIO << EOF
echo "Ejecutando WOL en servidor intermedio..."

# Verificar si wakeonlan está instalado
if ! command -v wakeonlan &> /dev/null; then
    echo "Instalando wakeonlan en servidor..."
    sudo apt-get update && sudo apt-get install -y wakeonlan || \
    sudo yum install -y wakeonlan || \
    sudo pacman -S wakeonlan
fi

# Enviar magic packet
echo "Enviando magic packet a $PLASMA_MAC..."
wakeonlan $PLASMA_MAC

echo "Magic packet enviado desde servidor intermedio"
EOF

if [ $? -eq 0 ]; then
    echo "✅ Comando ejecutado exitosamente en servidor intermedio"
else
    echo "❌ Error ejecutando comando en servidor intermedio"
    exit 1
fi

echo ""
echo "⏳ Esperando que el Plasma despierte (60 segundos)..."

# Verificar desde el servidor intermedio si el Plasma responde
for i in {60..1}; do
    if ssh -p $PUERTO_SSH $USUARIO_SSH@$SERVIDOR_INTERMEDIO "ping -c 1 $PLASMA_IP > /dev/null 2>&1" 2>/dev/null; then
        TIEMPO_TRANSCURRIDO=$((61-i))
        echo ""
        echo "🎉 ¡Plasma despertó exitosamente!"
        echo "⏱️ Tiempo: $TIEMPO_TRANSCURRIDO segundos"
        echo ""
        echo "🖥️ Ahora puedes conectarte via RustDesk:"
        echo "   ID: 400 661 147"
        echo ""
        exit 0
    fi
    
    if [ $((i % 10)) -eq 0 ]; then
        echo "⏳ Esperando... $i segundos restantes"
    fi
    
    sleep 1
done

echo ""
echo "⚠️ El Plasma no respondió en 60 segundos"
echo "Puede que tarde más en iniciar o haya un problema"
echo ""
echo "💡 Intenta conectar via RustDesk en unos minutos"
