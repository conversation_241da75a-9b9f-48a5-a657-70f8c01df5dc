#!/bin/bash

# Script de diagnóstico completo para Wake-On-LAN
# Ayuda a identificar problemas de configuración

PLASMA_IP="*************"
PLASMA_MAC="fc:34:97:e1:9a:9b"

echo "🔧 === DIAGNÓSTICO WAKE-ON-LAN ==="
echo "Sistema objetivo: $PLASMA_IP (MAC: $PLASMA_MAC)"
echo ""

# 1. Verificar conectividad básica
echo "1️⃣ Verificando conectividad de red..."
if ping -c 2 $PLASMA_IP > /dev/null 2>&1; then
    echo "❌ El sistema está ENCENDIDO. Apágalo completamente para probar WOL"
    exit 1
else
    echo "✅ El sistema está apagado (correcto para prueba WOL)"
fi
echo ""

# 2. Verificar que wakeonlan esté instalado
echo "2️⃣ Verificando wakeonlan..."
if command -v wakeonlan &> /dev/null; then
    echo "✅ wakeonlan está instalado"
    wakeonlan --version
else
    echo "❌ wakeonlan NO está instalado"
    echo "Instalar con: sudo pacman -S wakeonlan"
    exit 1
fi
echo ""

# 3. Verificar configuración de red local
echo "3️⃣ Verificando configuración de red local..."
echo "Interfaces de red activas:"
ip addr show | grep -E "inet.*192\.168\." | head -3
echo ""

# 4. Verificar tabla ARP (para confirmar MAC)
echo "4️⃣ Verificando tabla ARP..."
echo "Buscando $PLASMA_IP en tabla ARP:"
arp -a | grep $PLASMA_IP || echo "No encontrado en ARP (normal si está apagado)"
echo ""

# 5. Probar diferentes métodos de WOL
echo "5️⃣ Probando Wake-On-LAN..."
echo "Enviando magic packet a $PLASMA_MAC..."

# Método 1: wakeonlan básico
echo "Método 1: wakeonlan básico"
wakeonlan $PLASMA_MAC
sleep 2

# Método 2: wakeonlan con puerto específico
echo "Método 2: wakeonlan con puerto 9"
wakeonlan -p 9 $PLASMA_MAC
sleep 2

# Método 3: wakeonlan con interfaz específica
echo "Método 3: wakeonlan con interfaz ethernet"
INTERFACE=$(ip route | grep default | grep -o 'dev [^ ]*' | cut -d' ' -f2 | head -1)
echo "Usando interfaz: $INTERFACE"
wakeonlan -i $INTERFACE $PLASMA_MAC
sleep 2

echo ""
echo "6️⃣ Esperando respuesta (30 segundos)..."
for i in {30..1}; do
    if ping -c 1 $PLASMA_IP > /dev/null 2>&1; then
        echo ""
        echo "🎉 ¡ÉXITO! El sistema respondió en $((31-i)) segundos"
        echo "Wake-On-LAN está funcionando correctamente"
        exit 0
    fi
    
    if [ $((i % 5)) -eq 0 ]; then
        echo "⏳ Esperando... $i segundos restantes"
    fi
    sleep 1
done

echo ""
echo "❌ El sistema NO respondió después de 30 segundos"
echo ""
echo "🔍 POSIBLES PROBLEMAS:"
echo "1. Wake-On-LAN no habilitado en BIOS/UEFI"
echo "2. Wake-On-LAN no habilitado en el sistema operativo"
echo "3. Cable ethernet desconectado"
echo "4. MAC address incorrecta"
echo "5. Switch/router no soporta WOL"
echo "6. Firewall bloqueando magic packets"
echo ""
echo "🛠️ PRÓXIMOS PASOS:"
echo "1. Verificar BIOS: buscar 'Power On by PCI-E' o 'PME Event Wake Up'"
echo "2. En el Plasma, ejecutar: sudo ethtool enp1s0 | grep Wake-on"
echo "3. Verificar MAC real: ip link show"
echo "4. Probar con cable ethernet directo (sin switch)"
echo ""
echo "💡 ALTERNATIVAS:"
echo "1. Usar SSH para despertar remotamente"
echo "2. Configurar auto-encendido por horario en BIOS"
echo "3. Usar herramientas de gestión remota (IPMI, iLO, etc.)"
