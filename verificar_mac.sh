#!/bin/bash

echo "🔍 === VERIFICACIÓN DE MAC ADDRESS ==="
echo ""

# Verificar MAC desde el sistema local
echo "1️⃣ MAC addresses en este sistema (XFCE):"
ip link show | grep -E "(enp|eth|wlp)" -A 1

echo ""
echo "2️⃣ Verificando conectividad con Plasma..."
PLASMA_IP="*************"

if ping -c 2 $PLASMA_IP > /dev/null 2>&1; then
    echo "✅ Plasma está encendido"
    echo ""
    echo "3️⃣ Obteniendo MAC real del Plasma desde ARP:"
    
    # Instalar net-tools si no está disponible
    if ! command -v arp &> /dev/null; then
        echo "Instalando net-tools para comando arp..."
        sudo pacman -S net-tools
    fi
    
    # Hacer ping para actualizar tabla ARP
    ping -c 1 $PLASMA_IP > /dev/null 2>&1
    
    # Mostrar entrada ARP
    arp -a | grep $PLASMA_IP
    
    echo ""
    echo "4️⃣ También puedes verificar en el Plasma ejecutando:"
    echo "   ip link show"
    echo "   sudo ethtool enp1s0 | grep Wake-on"
    
else
    echo "❌ Plasma está apagado"
    echo "Enciende el Plasma y ejecuta este script de nuevo"
fi

echo ""
echo "📝 MAC actual en el script: fc:34:97:e1:9a:9b"
echo "Compara con la MAC real mostrada arriba"
