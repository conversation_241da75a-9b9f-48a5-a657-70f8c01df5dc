# 🌐 Guía: Wake-On-LAN Remoto

## Método 1: Port Forwarding en Router

### Paso 1: Configurar router
1. **Accede a tu router** (generalmente *********** o ***********)
2. **Ve a "Port Forwarding" o "Reenvío de puertos"**
3. **Configura:**
   - Puerto externo: `9999` (o el que prefieras)
   - Puerto interno: `9`
   - Protocolo: `UDP`
   - IP destino: `**************` (broadcast de tu red)

### Paso 2: Script para WOL remoto
```bash
# Desde internet usar:
wakeonlan -i TU_IP_PUBLICA -p 9999 fc:34:97:e1:9a:9b
```

## Método 2: VPN (Más seguro)

### Configurar VPN en router o servidor
1. **WireGuard** o **OpenVPN** en router
2. **Conectar desde exterior** via VPN
3. **Usar WOL normal** como si estuvieras en red local

## Método 3: Servidor intermedio

### Raspberry Pi o servidor siempre encendido
1. **Servidor en red local** siempre activo
2. **SSH al servidor** desde exterior
3. **Ejecutar WOL** desde el servidor

## Método 4: Servicios cloud especializados

### TeamViewer WOL, Chrome Remote Desktop, etc.
- Algunos servicios ofrecen WOL integrado
- Más fácil pero menos control

## ⚠️ Consideraciones de seguridad

- **Port forwarding** expone tu red
- **VPN es más seguro** pero más complejo
- **Cambiar puertos por defecto** siempre
- **Usar firewall** adicional si es posible
