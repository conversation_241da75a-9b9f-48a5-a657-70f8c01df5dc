#!/bin/bash

# Script para configurar apagado automático en el sistema Plasma
# Ejecutar este script EN EL SISTEMA PLASMA (no en XFCE)

echo "⏰ === CONFIGURACIÓN APAGADO AUTOMÁTICO ==="
echo "Este script debe ejecutarse EN EL SISTEMA PLASMA"
echo ""

# Verificar si estamos en el sistema correcto
if [[ $(hostname) != *"plasma"* ]] && [[ $(whoami) != *"plasma"* ]]; then
    echo "⚠️  ADVERTENCIA: Parece que no estás en el sistema Plasma"
    echo "Este script debe ejecutarse en el sistema que se va a apagar automáticamente"
    echo ""
    read -p "¿Continuar de todas formas? (s/N): " continuar
    if [[ $continuar != "s" && $continuar != "S" ]]; then
        echo "Cancelado"
        exit 1
    fi
fi

echo "🔧 Configurando apagado automático..."
echo ""

# Horarios sugeridos
echo "📅 Horarios recomendados para 8h de uso diario:"
echo "1. 9:00 AM - 6:00 PM (Horario laboral)"
echo "2. 10:00 AM - 7:00 PM (Horario flexible)"
echo "3. 8:00 AM - 5:00 PM (Horario temprano)"
echo "4. Personalizado"
echo ""

read -p "Selecciona opción (1-4): " opcion

case $opcion in
    1)
        HORA_APAGADO="18:00"
        DESCRIPCION="6:00 PM"
        ;;
    2)
        HORA_APAGADO="19:00"
        DESCRIPCION="7:00 PM"
        ;;
    3)
        HORA_APAGADO="17:00"
        DESCRIPCION="5:00 PM"
        ;;
    4)
        read -p "Ingresa hora de apagado (formato HH:MM): " HORA_APAGADO
        DESCRIPCION="$HORA_APAGADO"
        ;;
    *)
        echo "Opción inválida, usando 6:00 PM por defecto"
        HORA_APAGADO="18:00"
        DESCRIPCION="6:00 PM"
        ;;
esac

echo ""
echo "⚙️ Configurando apagado automático a las $DESCRIPCION..."

# Crear script de apagado
cat > /tmp/apagado_automatico.sh << 'EOF'
#!/bin/bash
# Script de apagado automático con notificación

# Notificar 10 minutos antes
notify-send "Sistema" "El sistema se apagará en 10 minutos" -t 10000

# Esperar 10 minutos
sleep 600

# Notificación final
notify-send "Sistema" "Apagando sistema en 30 segundos..." -t 30000

# Esperar 30 segundos
sleep 30

# Apagar sistema
/sbin/shutdown -h now
EOF

# Hacer ejecutable
chmod +x /tmp/apagado_automatico.sh

# Mover a ubicación permanente
sudo mv /tmp/apagado_automatico.sh /usr/local/bin/apagado_automatico.sh

# Configurar crontab
echo "📝 Configurando crontab..."

# Calcular minutos y horas para crontab (10 minutos antes del apagado)
HORA_NOTIF=$(date -d "$HORA_APAGADO 10 minutes ago" +"%H:%M")
MINUTOS=$(echo $HORA_NOTIF | cut -d: -f2)
HORAS=$(echo $HORA_NOTIF | cut -d: -f1)

# Agregar entrada a crontab
(crontab -l 2>/dev/null; echo "$MINUTOS $HORAS * * 1-5 /usr/local/bin/apagado_automatico.sh") | crontab -

echo "✅ Configuración completada"
echo ""
echo "📋 Resumen:"
echo "   • Apagado automático: $DESCRIPCION"
echo "   • Días: Lunes a Viernes"
echo "   • Notificación: 10 minutos antes"
echo "   • Script: /usr/local/bin/apagado_automatico.sh"
echo ""
echo "🔧 Para modificar o eliminar:"
echo "   crontab -e    # Editar horarios"
echo "   crontab -l    # Ver configuración actual"
echo ""
echo "⚠️ IMPORTANTE: Configura también el auto-encendido en BIOS"
echo "   BIOS → Avanzada → APM → Power On by RTC"
