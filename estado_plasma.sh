#!/bin/bash

# Script para verificar estado del Plasma y tiempo restante

PLASMA_IP="*************"

echo "📊 === ESTADO DEL SISTEMA PLASMA ==="
echo ""

# Verificar si está encendido
echo "🔍 Verificando estado..."
if ping -c 2 $PLASMA_IP > /dev/null 2>&1; then
    echo "✅ Sistema Plasma: ENCENDIDO"
    echo "🖥️ IP: $PLASMA_IP"
    echo "🆔 RustDesk ID: 400 661 147"
    
    # Obtener hora actual
    HORA_ACTUAL=$(date +"%H:%M")
    echo "🕐 Hora actual: $HORA_ACTUAL"
    
    # Calcular tiempo hasta apagado (asumiendo 18:00)
    HORA_APAGADO="18:00"
    SEGUNDOS_ACTUALES=$(date -d "$HORA_ACTUAL" +%s)
    SEGUNDOS_APAGADO=$(date -d "$HORA_APAGADO" +%s)
    
    if [ $SEGUNDOS_APAGADO -gt $SEGUNDOS_ACTUALES ]; then
        DIFERENCIA=$((SEGUNDOS_APAGADO - SEGUNDOS_ACTUALES))
        HORAS_RESTANTES=$((DIFERENCIA / 3600))
        MINUTOS_RESTANTES=$(((DIFERENCIA % 3600) / 60))
        
        echo "⏰ Tiempo hasta apagado: ${HORAS_RESTANTES}h ${MINUTOS_RESTANTES}m"
    else
        echo "⏰ El sistema debería apagarse pronto o ya pasó la hora"
    fi
    
    echo ""
    echo "🚀 Para conectar:"
    echo "   1. Abrir RustDesk"
    echo "   2. Conectar a: 400 661 147"
    
else
    echo "💤 Sistema Plasma: APAGADO"
    echo ""
    echo "📅 Próximo encendido automático:"
    echo "   • Lunes a Viernes: 9:00 AM"
    echo "   • Duración: 8 horas (hasta 6:00 PM)"
    echo ""
    echo "🔧 Para encender manualmente:"
    echo "   ./despertar_plasma_rustdesk.sh"
fi

echo ""
echo "📋 Configuración actual:"
echo "   • Auto-encendido: 9:00 AM (L-V)"
echo "   • Auto-apagado: 6:00 PM (L-V)"
echo "   • Uso diario: 8 horas"
